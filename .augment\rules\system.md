---
type: "always_apply"
---

<SystemRules>
  <Environment>Windows OS</Environment>

  <Editing>
    <CheckExistingCode>true</CheckExistingCode>
    <DoubleCheckLineNumbers>true</DoubleCheckLineNumbers>
    <ReadLinesBeforeEditing>true</ReadLinesBeforeEditing>
    <PreserveFunctionality>true</PreserveFunctionality>
    <DoubleCheckAfterEdits>true</DoubleCheckAfterEdits>
  </Editing>

  <CodeStructure>
    <Goal>
      Maintain a scalable, modular codebase by keeping files concise, separating concerns, 
      and using consistent structure across components.
    </Goal>
    <FileStructure>
      <OneResponsibilityPerFile>true</OneResponsibilityPerFile>
      <MaxFileLength>500</MaxFileLength>
      <ModularDesign>true</ModularDesign>
      <UseRelativeImports>true</UseRelativeImports>
      <ExampleStructure>
        <UIComponent>component/Button.js</UIComponent>
        <APIHandler>api/fetchUser.js</APIHandler>
        <Utility>utils/dateFormat.js</Utility>
      </ExampleStructure>
    </FileStructure>
  </CodeStructure>

  <Philosophy>
    <Principle name="KISS">Simplicity should be prioritized. Choose straightforward solutions over clever or complex ones.</Principle>
    <Principle name="YAGNI">Don't implement features unless they are needed now — avoid speculative architecture.</Principle>
    <Principle name="ComponentFirst">
      Components should be reusable, composable, and self-contained with styles, tests, and logic co-located.
    </Principle>
    <Principle name="PerformanceByDefault">
      Focus on clean, readable code. Let React 19's compiler optimize performance.
    </Principle>
  </Philosophy>

  <DesignPrinciples>
    <VerticalSliceArchitecture>true</VerticalSliceArchitecture>
    <CompositionOverInheritance>true</CompositionOverInheritance>
    <FailFastValidation>true</FailFastValidation>
  </DesignPrinciples>

  <AIBehavior>
    <NoAssumptions>true</NoAssumptions>
    <NoHallucinatedFunctions>true</NoHallucinatedFunctions>
    <ConfirmPathsAndModules>true</ConfirmPathsAndModules>
    <NeverDeleteExistingCode>true</NeverDeleteExistingCode>
    <FollowRules>true</FollowRules>
  </AIBehavior>

  <ImplementationRules>
    <Rule>Always follow the implementation plan generated by the Context Agent.</Rule>
    <Rule>Do not make any changes without valid context and plan.</Rule>
    <Rule>Maintain all existing functionality and UI.</Rule>
    <Rule>Ensure backwards compatibility.</Rule>
    <Rule>Complete all related updates; no partial implementations.</Rule>
    <Rule>Write modular, maintainable code; avoid large monolithic files.</Rule>
  </ImplementationRules>

  <Guidelines>
    <ContextAwareness>true</ContextAwareness>
    <ReuseUtilities>true</ReuseUtilities>
    <PreferComposition>true</PreferComposition>
    <PatternConsistency>true</PatternConsistency>
    <CheckOtherDomains>true</CheckOtherDomains>
  </Guidelines>

  <CommonPitfalls>
    <AvoidDuplicateFunctionality>true</AvoidDuplicateFunctionality>
    <DoNotOverwriteTests>true</DoNotOverwriteTests>
    <AvoidModifyingCoreFrameworksWithoutPermission>true</AvoidModifyingCoreFrameworksWithoutPermission>
    <CheckForExistingDependencies>true</CheckForExistingDependencies>
  </CommonPitfalls>

  <Workflow>
    <TestFirstDevelopment preferred="true" />
    <ThinkHardBeforeArchitecting>true</ThinkHardBeforeArchitecting>
    <BreakTasksIntoUnits>true</BreakTasksIntoUnits>
    <ValidateBeforeBuilding>true</ValidateBeforeBuilding>
  </Workflow>
</SystemRules>
